import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { MoreHorizontal, Eye, Edit } from 'lucide-react';
import { useState } from 'react';
import { Order } from '../../lib/types';
import PopupModal from '@/components/shared/popup-modal';
import OrderDetailsModal from '../forms/order-details-modal';
import OrderUpdateModal from '../forms/order-update-modal';
import { usePermissions } from '@/contexts/PermissionsContext';

interface CellActionProps {
  data: Order;
  order: Order;
}

export const CellAction: React.FC<CellActionProps> = ({ data }) => {
  const { hasPermission } = usePermissions();
  const canUpdate = hasPermission('orders', 'update');
  const canShow = hasPermission('orders', 'show');

  return (
    <>
      <DropdownMenu modal={false}>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0">
            <span className="sr-only">Open menu</span>
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuLabel>Actions</DropdownMenuLabel>

          <div className="flex flex-col gap-2">
            {canShow && (
              <PopupModal
                title="View Details"
                icon="Eye"
                renderModal={(onClose) => (
                  <OrderDetailsModal modalClose={onClose} orderId={data.id} />
                )}
              />
            )}
            {canUpdate && (
              <PopupModal
                title="Update Order"
                icon="Edit"
                renderModal={(onClose) => (
                  <OrderUpdateModal modalClose={onClose} order={data} />
                )}
              />
            )}
          </div>
        </DropdownMenuContent>
      </DropdownMenu>
    </>
  );
};
