import Heading from '@/components/shared/heading';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useFetchOrderDetails } from '../../hooks/useFetchOrderDetails';
import { Loader2 } from 'lucide-react';

interface OrderDetailsModalProps {
  modalClose: () => void;
  orderId: number;
}

const OrderDetailsModal = ({ modalClose, orderId }: OrderDetailsModalProps) => {
  const { order, isLoading, error } = useFetchOrderDetails(orderId);

  // Loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading order details...</span>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="p-6">
        <div className="text-center text-red-600">
          <p>Error loading order details</p>
          <Button onClick={modalClose} className="mt-4">
            Close
          </Button>
        </div>
      </div>
    );
  }

  // Order not found state
  if (!order) {
    return (
      <div className="p-6">
        <div className="text-center">
          <p>Order not found</p>
          <Button onClick={modalClose} className="mt-4">
            Close
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-6">
      <div className="flex items-center justify-between">
        <Heading
          title={`Order #${order.order_number}`}
          description="Order Details"
        />
        <Button variant="outline" onClick={modalClose}>
          Close
        </Button>
      </div>

      <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
        {/* Order Information */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Order Information</h3>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="font-medium">Status:</span>
              <Badge>{order.status}</Badge>
            </div>
            <div className="flex justify-between">
              <span className="font-medium">Price:</span>
              <span>
                {order.price !== undefined && order.price !== null
                  ? `$${order.price.toFixed(2)}`
                  : 'N/A'}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium">Delivery Cost:</span>
              <span>
                {order.delivery_cost !== undefined &&
                order.delivery_cost !== null
                  ? `$${order.delivery_cost.toFixed(2)}`
                  : 'N/A'}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium">Total Price:</span>
              <span>
                {order.total_price !== undefined && order.total_price !== null
                  ? `$${order.total_price.toFixed(2)}`
                  : 'N/A'}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium">Created:</span>
              <span>{new Date(order.created_at).toLocaleString()}</span>
            </div>
          </div>
        </div>

        {/* Customer Information */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Customer Information</h3>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="font-medium">Customer Phone:</span>
              <span>{order.customer_phone}</span>
            </div>
          </div>
        </div>

        {/* Restaurant Information */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Restaurant Information</h3>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="font-medium">Restaurant ID:</span>
              <span>{order.restaurant_id}</span>
            </div>
            {order.restaurant && (
              <>
                <div className="flex justify-between">
                  <span className="font-medium">Name:</span>
                  <span>{order.restaurant.name_en}</span>
                </div>
                <div className="flex justify-between">
                  <span className="font-medium">Phone:</span>
                  <span>{order.restaurant.phone}</span>
                </div>
                <div className="flex justify-between">
                  <span className="font-medium">Address:</span>
                  <span>{order.restaurant.address_en}</span>
                </div>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Location Information */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Location Information</h3>
        <div className="space-y-2">
          <div className="flex justify-between">
            <span className="font-medium">Pickup Location:</span>
            <span>
              {order.pickup_latitude}, {order.pickup_longitude}
            </span>
          </div>
          <div className="flex justify-between">
            <span className="font-medium">Delivery Location:</span>
            <span>
              {order.delivery_latitude}, {order.delivery_longitude}
            </span>
          </div>
          {order.distance && (
            <div className="flex justify-between">
              <span className="font-medium">Distance:</span>
              <span>{order.distance} km</span>
            </div>
          )}
          {order.estimated_time && (
            <div className="flex justify-between">
              <span className="font-medium">Estimated Time:</span>
              <span>{order.estimated_time}</span>
            </div>
          )}
          {order.notes && (
            <div className="flex justify-between">
              <span className="font-medium">Notes:</span>
              <span>{order.notes}</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default OrderDetailsModal;
