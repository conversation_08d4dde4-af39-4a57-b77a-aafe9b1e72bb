export interface Restaurant {
  id: number;
  name_en: string;
  name_ar: string;
  name_tr: string;
  email: string;
  phone: string;
  contact_number: string;
  address_en: string;
  address_ar: string;
  address_tr: string;
  latitude: number;
  longitude: number;
  start_time: string;
  end_time: string;
  is_available: boolean;
  status: 'PENDING' | 'APPROVED' | 'REJECTED' | 'SUSPENDED';
  logo?: {
    id: number;
    url: string;
    path: string;
  };
  created_at: string;
  updated_at: string;
}

export interface Customer {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
  phone: string;
  created_at: string;
  updated_at: string;
}

export interface Driver {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
  phone_number: string;
  status: 'ACTIVE' | 'INACTIVE';
  created_at: string;
  updated_at: string;
}

export interface OrderItem {
  id: number;
  order_id: number;
  product_name: string;
  quantity: number;
  price: number;
  total: number;
  created_at: string;
  updated_at: string;
}

export interface Order {
  id: number;
  restaurant_id: number;
  order_number: string;
  customer_phone: string;
  status:
    | 'PENDING'
    | 'ASSIGNED'
    | 'ON_THE_WAY'
    | 'DELIVERED'
    | 'ARCHIVED'
    | 'CANCELLED';
  pickup_latitude: number;
  pickup_longitude: number;
  delivery_latitude: number;
  delivery_longitude: number;
  distance: number | null;
  price: number;
  delivery_cost: number | null;
  total_price: number | null;
  estimated_time: string | null;
  notes: string | null;
  created_at: string;

  // Relations (if included in API response)
  restaurant?: Restaurant;
}

export interface OrderFilters {
  status?: Order['status'];
  order_number?: string;
}

// API Response interfaces
export interface OrderDetailsResponse {
  status: boolean;
  message: string;
  data: Order;
}

export interface OrdersListResponse {
  status: boolean;
  message: string;
  data: {
    items: Order[];
    pagination: {
      current_page: number;
      last_page: number;
      from: number;
      to: number;
      per_page: number;
      total: number;
    };
  };
}
