import { useQuery } from '@tanstack/react-query';
import { client, setHeaderToken, refreshAuth } from '../../../lib/axiosClient';
import { redirect } from 'react-router-dom';
import toast from 'react-hot-toast';
import { OrderDetailsResponse } from '../lib/types';

export const fetchOrderDetails = async (
  orderId: number
): Promise<OrderDetailsResponse> => {
  const token = localStorage.getItem('token');

  // Build query parameters to include restaurant details if available
  const params = new URLSearchParams();
  params.append('include', 'restaurant');

  try {
    const response = await client.get(
      `/admin/orders/${orderId}?${params.toString()}`,
      {
        headers: {
          Authorization: `Bearer ${token}`
        }
      }
    );

    return response.data;
  } catch (error: any) {
    console.log('error', error);

    if (error.response?.status === 401) {
      const newToken = await refreshAuth();

      if (newToken) {
        setHeaderToken(newToken);
        const retryResponse = await client.get(
          `/admin/orders/${orderId}?${params.toString()}`,
          {
            headers: {
              Authorization: `Bearer ${newToken}`
            }
          }
        );
        return retryResponse.data;
      } else {
        localStorage.removeItem('token');
        toast.error('something went wrong');
        redirect('/login');
      }
    } else {
      throw error;
    }
  }
};

export function useFetchOrderDetails(orderId: number) {
  const { data, isLoading, error } = useQuery({
    queryKey: ['order-details', orderId],
    queryFn: () => fetchOrderDetails(orderId),
    enabled: !!orderId // Only run query if orderId is provided
  });

  return {
    order: data?.data,
    isLoading,
    error
  };
}
