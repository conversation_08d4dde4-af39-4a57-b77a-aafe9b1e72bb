import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { MoreHorizontal, Ban } from 'lucide-react';
import { Manager } from '../../lib/types';
import PopupModal from '@/components/shared/popup-modal';
import ManagerForm from '../forms/manager-form';
import { useUpdateManager } from '../../hooks/useUpdateManager';
import toast from 'react-hot-toast';
import { useState } from 'react';

interface CellActionProps {
  data: Manager;
  manager: Manager;
}

export const CellAction: React.FC<CellActionProps> = ({ data }) => {
  const [showSuspendAlert, setShowSuspendAlert] = useState(false);
  const { mutate: updateManager, isPending: isUpdating } = useUpdateManager();

  const handleSuspend = () => {
    const updateData = {
      username: data.username,
      email: data.email,
      status: 'INACTIVE',
      profile_image: data.profile_image.path
    };

    updateManager(
      { id: data.id, data: updateData },
      {
        onSuccess: () => {
          toast.success('Manager suspended successfully');
          setShowSuspendAlert(false);
        },
        onError: () => {
          toast.error('Failed to suspend manager');
        }
      }
    );
  };

  const canSuspend = data.status === 'ACTIVE';

  return (
    <>
      <DropdownMenu modal={false}>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0">
            <span className="sr-only">Open menu</span>
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuLabel>Actions</DropdownMenuLabel>
          <div className="flex flex-col gap-2">
            <PopupModal
              title="Update Manager"
              icon="Edit"
              renderModal={(onClose) => (
                <ManagerForm modalClose={onClose} manager={data} />
              )}
            />

            {/* Suspend Manager */}
            {canSuspend && (
              <Button
                onClick={() => setShowSuspendAlert(true)}
                className="w-full justify-start p-2"
                variant="ghost"
                disabled={isUpdating}
              >
                <Ban className="mr-2 h-4 w-4 text-orange-600" />
                Suspend
              </Button>
            )}
          </div>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Suspend Confirmation */}
      {showSuspendAlert && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="mx-4 w-full max-w-sm rounded-lg bg-white p-6">
            <h3 className="mb-2 text-lg font-semibold">Suspend Manager</h3>
            <p className="mb-6 text-sm text-gray-600">
              Are you sure you want to suspend this manager? This will change
              their status to INACTIVE.
            </p>
            <div className="flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => setShowSuspendAlert(false)}
                disabled={isUpdating}
              >
                Cancel
              </Button>
              <Button
                onClick={handleSuspend}
                disabled={isUpdating}
                className="bg-orange-600 hover:bg-orange-700"
              >
                {isUpdating ? 'Suspending...' : 'Suspend'}
              </Button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};
