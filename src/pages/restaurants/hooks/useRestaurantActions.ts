import { useMutation, useQueryClient } from '@tanstack/react-query';
import { client, setHeaderToken, refreshAuth } from '../../../lib/axiosClient';
import { redirect } from 'react-router-dom';
import toast from 'react-hot-toast';

// Approve Restaurant
const approveRestaurant = async (id: number) => {
  const token = localStorage.getItem('token');

  try {
    const response = await client.put(
      `/admin/restaurants/${id}/approve`,
      {},
      {
        headers: {
          Authorization: `Bearer ${token}`
        }
      }
    );
    return response.data;
  } catch (error: any) {
    if (error.response?.status === 401) {
      const newToken = await refreshAuth();
      if (newToken) {
        setHeaderToken(newToken);
        const retryResponse = await client.put(
          `/admin/restaurants/${id}/approve`,
          {},
          {
            headers: {
              Authorization: `Bearer ${newToken}`
            }
          }
        );
        return retryResponse.data;
      } else {
        localStorage.removeItem('token');
        toast.error('something went wrong');
        redirect('/login');
      }
    } else {
      throw error;
    }
  }
};

// Reject Restaurant
const rejectRestaurant = async ({
  id,
  rejection_reason
}: {
  id: number;
  rejection_reason: string;
}) => {
  const token = localStorage.getItem('token');

  try {
    const response = await client.put(
      `/admin/restaurants/${id}/reject`,
      {
        rejection_reason
      },
      {
        headers: {
          Authorization: `Bearer ${token}`
        }
      }
    );
    return response.data;
  } catch (error: any) {
    if (error.response?.status === 401) {
      const newToken = await refreshAuth();
      if (newToken) {
        setHeaderToken(newToken);
        const retryResponse = await client.put(
          `/admin/restaurants/${id}/reject`,
          {
            rejection_reason
          },
          {
            headers: {
              Authorization: `Bearer ${newToken}`
            }
          }
        );
        return retryResponse.data;
      } else {
        localStorage.removeItem('token');
        toast.error('something went wrong');
        redirect('/login');
      }
    } else {
      throw error;
    }
  }
};

// Suspend Restaurant
const suspendRestaurant = async (id: number) => {
  const token = localStorage.getItem('token');

  try {
    const response = await client.put(
      `/admin/restaurants/${id}/suspend`,
      {},
      {
        headers: {
          Authorization: `Bearer ${token}`
        }
      }
    );
    return response.data;
  } catch (error: any) {
    if (error.response?.status === 401) {
      const newToken = await refreshAuth();
      if (newToken) {
        setHeaderToken(newToken);
        const retryResponse = await client.put(
          `/admin/restaurants/${id}/suspend`,
          {},
          {
            headers: {
              Authorization: `Bearer ${newToken}`
            }
          }
        );
        return retryResponse.data;
      } else {
        localStorage.removeItem('token');
        toast.error('something went wrong');
        redirect('/login');
      }
    } else {
      throw error;
    }
  }
};

export function useApproveRestaurant() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: approveRestaurant,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['restaurants'] });
    }
  });
}

export function useRejectRestaurant() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: rejectRestaurant,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['restaurants'] });
    }
  });
}

export function useSuspendRestaurant() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: suspendRestaurant,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['restaurants'] });
    }
  });
}
