import { useQuery } from '@tanstack/react-query';
import { client, setHeaderToken, refreshAuth } from '../../../lib/axiosClient';
import { redirect } from 'react-router-dom';
import toast from 'react-hot-toast';

export const fetchRestaurants = async (page: number) => {
  const token = localStorage.getItem('token');

  try {
    const response = await client.get(`/admin/restaurants?page=${page}`, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });

    return response.data;
  } catch (error: any) {
    console.log('error', error);

    if (error.response?.status === 401) {
      const newToken = await refreshAuth();

      if (newToken) {
        setHeaderToken(newToken);
        const retryResponse = await client.get(
          `/admin/restaurants?page=${page}`,
          {
            headers: {
              Authorization: `Bearer ${newToken}`
            }
          }
        );
        return retryResponse.data;
      } else {
        localStorage.removeItem('token');
        toast.error('something went wrong');
        redirect('/login');
      }
    } else {
      throw error;
    }
  }
};

export function useFetchRestaurants(page: number) {
  const { data, isLoading } = useQuery({
    queryKey: ['restaurants', page],
    queryFn: () => fetchRestaurants(page)
  });

  return {
    restaurants: data?.data?.items,
    pagination: data?.data?.pagination,
    isLoading
  };
}
