import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { MoreHorizontal, CheckCircle, XCircle, Ban } from 'lucide-react';
import { Restaurant } from '../../lib/types';

import {
  useApproveRestaurant,
  useRejectRestaurant,
  useSuspendRestaurant
} from '../../hooks/useRestaurantActions';
import toast from 'react-hot-toast';
import { useState } from 'react';
import { AlertModal } from '@/components/shared/alert-modal';

interface CellActionProps {
  data: Restaurant;
}

export const CellAction: React.FC<CellActionProps> = ({ data }) => {
  const [showApproveAlert, setShowApproveAlert] = useState(false);
  const [showSuspendAlert, setShowSuspendAlert] = useState(false);
  const [showRejectModal, setShowRejectModal] = useState(false);
  const [rejectionReason, setRejectionReason] = useState('');

  const { mutate: approveRestaurant, isPending: isApproving } =
    useApproveRestaurant();
  const { mutate: rejectRestaurant, isPending: isRejecting } =
    useRejectRestaurant();
  const { mutate: suspendRestaurant, isPending: isSuspending } =
    useSuspendRestaurant();

  const handleApprove = () => {
    approveRestaurant(data.id, {
      onSuccess: () => {
        toast.success('Restaurant approved successfully');
        setShowApproveAlert(false);
      },
      onError: () => {
        toast.error('Failed to approve restaurant');
      }
    });
  };

  const handleReject = () => {
    if (!rejectionReason.trim()) {
      toast.error('Please provide a rejection reason');
      return;
    }

    rejectRestaurant(
      { id: data.id, rejection_reason: rejectionReason },
      {
        onSuccess: () => {
          toast.success('Restaurant rejected successfully');
          setShowRejectModal(false);
          setRejectionReason('');
        },
        onError: () => {
          toast.error('Failed to reject restaurant');
        }
      }
    );
  };

  const handleSuspend = () => {
    suspendRestaurant(data.id, {
      onSuccess: () => {
        toast.success('Restaurant suspended successfully');
        setShowSuspendAlert(false);
      },
      onError: () => {
        toast.error('Failed to suspend restaurant');
      }
    });
  };

  const canApprove = data.status === 'PENDING';
  const canReject = data.status === 'PENDING' || data.status === 'APPROVED';
  const canSuspend = data.status === 'APPROVED';

  return (
    <>
      <DropdownMenu modal={false}>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0">
            <span className="sr-only">Open menu</span>
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuLabel>Actions</DropdownMenuLabel>
          <div className="flex flex-col gap-2">
            {/* View Details */}
            {/* <PopupModal
              title="View Details"
              icon="Eye"
              renderModal={(onClose) => (
                <RestaurantForm modalClose={onClose} restaurant={data} />
              )}
            /> */}

            {/* Approve Restaurant */}
            {canApprove && (
              <Button
                onClick={() => setShowApproveAlert(true)}
                className="w-full justify-start p-2"
                variant="ghost"
                disabled={isApproving}
              >
                <CheckCircle className="mr-2 h-4 w-4 text-green-600" />
                Approve
              </Button>
            )}

            {/* Reject Restaurant */}
            {canReject && (
              <Button
                onClick={() => setShowRejectModal(true)}
                className="w-full justify-start p-2"
                variant="ghost"
                disabled={isRejecting}
              >
                <XCircle className="mr-2 h-4 w-4 text-red-600" />
                Reject
              </Button>
            )}

            {/* Suspend Restaurant */}
            {canSuspend && (
              <Button
                onClick={() => setShowSuspendAlert(true)}
                className="w-full justify-start p-2"
                variant="ghost"
                disabled={isSuspending}
              >
                <Ban className="mr-2 h-4 w-4 text-orange-600" />
                Suspend
              </Button>
            )}
          </div>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Approve Confirmation */}
      <AlertModal
        isOpen={showApproveAlert}
        onClose={() => setShowApproveAlert(false)}
        onConfirm={handleApprove}
        loading={isApproving}
        title="Approve Restaurant"
        description="Are you sure you want to approve this restaurant? This action will make the restaurant visible to customers."
      />

      {/* Suspend Confirmation */}
      <AlertModal
        isOpen={showSuspendAlert}
        onClose={() => setShowSuspendAlert(false)}
        onConfirm={handleSuspend}
        loading={isSuspending}
        title="Suspend Restaurant"
        description="Are you sure you want to suspend this restaurant? This will temporarily disable the restaurant."
      />

      {/* Reject Modal */}
      {showRejectModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className=" mx-4 w-full max-w-md rounded-lg p-6">
            <h3 className="mb-4 text-lg font-semibold">Reject Restaurant</h3>
            <p className="mb-4 text-sm text-gray-600">
              Please provide a reason for rejecting this restaurant:
            </p>
            <textarea
              value={rejectionReason}
              onChange={(e) => setRejectionReason(e.target.value)}
              placeholder="Enter rejection reason..."
              className="mb-4 h-24 w-full resize-none rounded-md border border-gray-300 p-3"
            />
            <div className="flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => {
                  setShowRejectModal(false);
                  setRejectionReason('');
                }}
                disabled={isRejecting}
              >
                Cancel
              </Button>
              <Button
                onClick={handleReject}
                disabled={isRejecting || !rejectionReason.trim()}
                className="bg-red-600 hover:bg-red-700"
              >
                {isRejecting ? 'Rejecting...' : 'Reject'}
              </Button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};
