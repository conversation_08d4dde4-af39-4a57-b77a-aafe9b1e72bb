import DataTable from '@/components/shared/data-table';
import { columns } from './table/columns';
import { useEffect, useState } from 'react';
import { DataTableSkeleton } from '@/components/shared/data-table-skeleton';
import RestaurantTableActions from './table/restuarant-table-action';
import { Restaurant } from '../lib/types';
import { useFetchRestaurants } from '../hooks/useFetchRestaurants';

export default function RestaurantTable() {
  const [page, setPage] = useState(1);
  const {
    restaurants,
    pagination,
    isLoading
  }: { restaurants: Restaurant[]; pagination: any; isLoading: boolean } =
    useFetchRestaurants(page);
  useEffect(() => {
    if (!isLoading) {
      setPage(pagination?.page);
    }
  }, [pagination?.page, isLoading]);

  const pageLimit = pagination?.per_page;
  const totalRestaurants = pagination?.total;
  const pageCount = Math.ceil(totalRestaurants! / pageLimit);

  return (
    <>
      <RestaurantTableActions />
      {isLoading ? (
        <div className="p-5">
          <DataTableSkeleton columnCount={8} />
        </div>
      ) : (
        <DataTable
          columns={columns}
          data={restaurants}
          pageCount={pageCount}
          onPageChange={setPage}
        />
      )}
    </>
  );
}
