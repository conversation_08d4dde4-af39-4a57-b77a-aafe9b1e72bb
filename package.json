{"name": "react-shadcn-dashboard", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "format": "prettier . --write", "preview": "vite preview", "prepare": "husky"}, "lint-staged": {"**/*": ["prettier --write --ignore-unknown"]}, "dependencies": {"@eslint/config-array": "^0.19.0", "@eslint/object-schema": "^2.1.4", "@hookform/resolvers": "^3.3.4", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-aspect-ratio": "^1.0.3", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-collapsible": "^1.0.3", "@radix-ui/react-context-menu": "^2.1.5", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-hover-card": "^1.0.7", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-menubar": "^1.0.4", "@radix-ui/react-navigation-menu": "^1.1.4", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-toggle": "^1.0.3", "@radix-ui/react-toggle-group": "^1.0.4", "@radix-ui/react-tooltip": "^1.0.7", "@tanstack/react-query": "^5.28.0", "@tanstack/react-query-devtools": "^5.28.0", "@tanstack/react-table": "^8.14.0", "axios": "^1.6.8", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "embla-carousel-react": "^8.0.0", "i18next": "^25.0.0", "i18next-browser-languagedetector": "^8.0.4", "i18next-http-backend": "^3.0.2", "input-otp": "^1.2.2", "lucide-react": "^0.358.0", "next-themes": "^0.3.0", "react": "^18.2.0", "react-day-picker": "^8.10.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-error-boundary": "^4.0.13", "react-helmet-async": "^2.0.4", "react-hook-form": "^7.51.1", "react-hot-toast": "^2.5.2", "react-i18next": "^15.4.1", "react-resizable-panels": "^2.0.13", "react-router-dom": "^6.22.3", "recharts": "^2.12.2", "sonner": "^1.4.41", "tailwind-merge": "^2.2.2", "tailwindcss-animate": "^1.0.7", "use-debounce": "^10.0.0", "vaul": "^0.9.0", "zod": "^3.22.4"}, "devDependencies": {"@types/node": "^20.11.27", "@types/react": "^18.2.64", "@types/react-dom": "^18.2.21", "@typescript-eslint/eslint-plugin": "^7.1.1", "@typescript-eslint/parser": "^7.1.1", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.18", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "husky": "^9.0.11", "lint-staged": "^15.2.2", "postcss": "^8.4.35", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.5.12", "tailwindcss": "^3.4.1", "typescript": "^5.2.2", "vite": "^5.1.6"}}